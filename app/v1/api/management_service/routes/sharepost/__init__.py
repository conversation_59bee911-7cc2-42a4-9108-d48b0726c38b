from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Any, List, Dict, Optional, <PERSON>ple
import uuid
import datetime
import tempfile
import os
import httpx
import asyncio
import time
from datetime import timezone
from bson import ObjectId

# Import proper authentication and models
from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.async_minio_client import create_async_minio_client
from app.shared.utils.logger import setup_new_logging

# Setup logging first
loggers = setup_new_logging(__name__)

# Video processing imports (with fallback for missing dependencies)
try:
    from moviepy.editor import ImageClip, AudioFileClip, concatenate_videoclips, CompositeVideoClip
    MOVIEPY_AVAILABLE = True
except ImportError as e:
    loggers.warning(f"MoviePy not available: {e}. Video generation will not work.")
    MOVIEPY_AVAILABLE = False
    # Create dummy classes to prevent import errors
    class ImageClip:
        pass
    class AudioFileClip:
        pass
    class CompositeVideoClip:
        pass
    def concatenate_videoclips(*_args, **_kwargs):
        raise HTTPException(status_code=500, detail="MoviePy not available for video generation")
router = APIRouter()

class GenerateVideoRequest(BaseModel):
    task_set_id: str
    include_audio: bool = True
    video_duration_per_image: float = 3.0  # seconds per image if no audio
    max_concurrent_downloads: int = 5  # Maximum concurrent downloads
    export_timeout: int = 120  # Maximum seconds for video export

class GenerateVideoResponse(BaseModel):
    video_url: str
    video_metadata: Dict[str, Any]
    processing_status: str

class VideoGenerationStatus(BaseModel):
    task_set_id: str
    status: str  # "processing", "completed", "failed"
    progress: float  # 0.0 to 1.0
    video_url: Optional[str] = None
    error_message: Optional[str] = None


async def download_media_file(url: str, temp_dir: str, filename: str) -> str:
    """Download a media file from URL to temporary directory."""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url)
            response.raise_for_status()

            file_path = os.path.join(temp_dir, filename)
            with open(file_path, 'wb') as f:
                f.write(response.content)

            loggers.info(f"Downloaded media file: {filename}")
            return file_path
    except Exception as e:
        loggers.error(f"Failed to download media file {url}: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Failed to download media: {str(e)}")


async def download_story_media(
    story: Dict[str, Any],
    story_index: int,
    temp_dir: str,
    include_audio: bool
) -> Tuple[str, Optional[str], float]:
    """Download both image and audio for a single story in parallel."""
    start_time = time.time()

    image_metadata = story.get("image_metadata", {})
    audio_metadata = story.get("audio_metadata", {})

    image_url = image_metadata.get("url")
    audio_url = audio_metadata.get("url") if include_audio else None

    if not image_url:
        raise ValueError(f"No image URL found for story stage {story.get('stage', story_index+1)}")

    # Prepare download tasks
    download_tasks = []

    # Image download task
    image_filename = f"image_{story_index+1}.jpg"
    image_task = download_media_file(image_url, temp_dir, image_filename)
    download_tasks.append(("image", image_task))

    # Audio download task (if available)
    audio_task = None
    if audio_url:
        audio_filename = f"audio_{story_index+1}.mp3"
        audio_task = download_media_file(audio_url, temp_dir, audio_filename)
        download_tasks.append(("audio", audio_task))

    # Execute downloads in parallel
    results = await asyncio.gather(*[task for _, task in download_tasks], return_exceptions=True)

    # Process results
    image_path = None
    audio_path = None

    for i, (media_type, _) in enumerate(download_tasks):
        result = results[i]
        if isinstance(result, Exception):
            if media_type == "image":
                raise result  # Image is required
            else:
                loggers.warning(f"Failed to download audio for story {story_index+1}: {str(result)}")
        else:
            if media_type == "image":
                image_path = result
            else:
                audio_path = result

    # Get audio duration if audio was downloaded
    audio_duration = None
    if audio_path:
        try:
            audio_clip = AudioFileClip(audio_path)
            audio_duration = audio_clip.duration
            audio_clip.close()
        except Exception as e:
            loggers.warning(f"Failed to get audio duration for story {story_index+1}: {str(e)}")
            audio_path = None

    download_time = time.time() - start_time
    loggers.info(f"Downloaded media for story {story_index+1} in {download_time:.2f}s")

    return image_path, audio_path, audio_duration


async def download_all_media_parallel(
    stories: List[Dict[str, Any]],
    temp_dir: str,
    include_audio: bool,
    max_concurrent: int = 5
) -> List[Tuple[str, Optional[str], Optional[float]]]:
    """Download all media files for stories in parallel with concurrency limit."""
    start_time = time.time()

    # Create semaphore to limit concurrent downloads
    semaphore = asyncio.Semaphore(max_concurrent)

    async def download_with_semaphore(story, index):
        async with semaphore:
            return await download_story_media(story, index, temp_dir, include_audio)

    # Create tasks for all stories
    tasks = [
        download_with_semaphore(story, i)
        for i, story in enumerate(stories)
    ]

    # Execute all downloads in parallel
    loggers.info(f"Starting parallel download of {len(stories)} stories with max {max_concurrent} concurrent downloads")
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Process results and handle exceptions
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            loggers.error(f"Failed to download media for story {i+1}: {str(result)}")
            # Skip this story
            continue
        else:
            processed_results.append(result)

    total_time = time.time() - start_time
    loggers.info(f"Completed parallel media download in {total_time:.2f}s for {len(processed_results)} stories")

    return processed_results


async def export_video_with_timeout(
    final_video,
    output_path: str,
    temp_dir: str,
    timeout_seconds: int = 120,
    include_audio: bool = True
) -> str:
    """Export video with timeout and fallback to simple export if needed."""

    async def export_with_full_settings():
        """Export with full audio/video settings."""
        final_video.write_videofile(
            output_path,
            fps=24,
            codec='libx264',
            audio_codec='aac' if include_audio else None,
            temp_audiofile=os.path.join(temp_dir, 'temp-audio.m4a') if include_audio else None,
            remove_temp=True,
            verbose=False,
            logger=None,
            preset='ultrafast',
            ffmpeg_params=['-crf', '23'],
            threads=4
        )
        return output_path

    async def export_fallback():
        """Fallback export with minimal settings for speed."""
        fallback_path = os.path.join(temp_dir, "slideshow_video_simple.mp4")
        loggers.warning("Using fallback export with simplified settings")
        final_video.write_videofile(
            fallback_path,
            fps=15,  # Lower FPS for speed
            codec='libx264',
            audio_codec=None,  # No audio in fallback
            verbose=False,
            logger=None,
            preset='ultrafast'
        )
        return fallback_path

    try:
        # Try export with timeout
        export_task = asyncio.create_task(asyncio.to_thread(export_with_full_settings))
        result = await asyncio.wait_for(export_task, timeout=timeout_seconds)
        return result

    except asyncio.TimeoutError:
        loggers.warning(f"Video export timed out after {timeout_seconds}s, using fallback")
        # Cancel the timed-out task
        export_task.cancel()

        # Try fallback export
        try:
            fallback_task = asyncio.create_task(asyncio.to_thread(export_fallback))
            result = await asyncio.wait_for(fallback_task, timeout=60)  # 60s timeout for fallback
            return result
        except asyncio.TimeoutError:
            raise HTTPException(
                status_code=500,
                detail="Video export failed: both primary and fallback exports timed out"
            )
    except Exception as e:
        loggers.error(f"Video export failed: {str(e)}")
        # Try fallback on any error
        try:
            return await asyncio.to_thread(export_fallback)
        except Exception as fallback_error:
            loggers.error(f"Fallback export also failed: {str(fallback_error)}")
            raise HTTPException(status_code=500, detail=f"Video export failed: {str(e)}")


async def fetch_task_set_stories(current_user: UserTenantDB, task_set_id: str) -> List[Dict[str, Any]]:
    """Fetch task set and its associated stories with media URLs."""
    try:
        # Fetch task set
        task_set = await current_user.async_db.task_sets.find_one({"_id": ObjectId(task_set_id)})
        if not task_set:
            raise HTTPException(status_code=404, detail="Task set not found")

        # Get story IDs from task set
        story_ids = task_set.get("stories", [])
        if not story_ids:
            raise HTTPException(status_code=400, detail="No stories found in task set")

        # Convert string IDs to ObjectIds if needed
        story_object_ids = []
        for story_id in story_ids:
            if isinstance(story_id, str):
                story_object_ids.append(ObjectId(story_id))
            else:
                story_object_ids.append(story_id)

        # Fetch stories with media metadata
        stories = await current_user.async_db.story_steps.find(
            {"_id": {"$in": story_object_ids}},
            {
                "stage": 1,
                "script": 1,
                "image": 1,
                "audio_metadata": 1,
                "image_metadata": 1,
                "created_at": 1
            }
        ).sort("stage", 1).to_list(length=None)

        if not stories:
            raise HTTPException(status_code=400, detail="No story data found")

        loggers.info(f"Found {len(stories)} stories for task set {task_set_id}")
        return stories

    except Exception as e:
        loggers.error(f"Error fetching task set stories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching stories: {str(e)}")


async def create_slideshow_video(
    stories: List[Dict[str, Any]],
    temp_dir: str,
    include_audio: bool = True,
    video_duration_per_image: float = 3.0,
    outro_duration: float = 3.0,
    max_concurrent_downloads: int = 5,
    export_timeout: int = 120
) -> str:
    """Create a slideshow video from stories with images and audio, with company outro image."""
    try:
        start_time = time.time()

        # Download all media files in parallel
        loggers.info(f"Starting parallel download for {len(stories)} stories with max {max_concurrent_downloads} concurrent")
        media_results = await download_all_media_parallel(stories, temp_dir, include_audio, max_concurrent_downloads)

        download_time = time.time() - start_time
        loggers.info(f"Parallel download completed in {download_time:.2f}s")

        # Create video clips from downloaded media
        video_clips = []
        total_duration = 0
        clip_creation_start = time.time()

        for i, (image_path, audio_path, audio_duration) in enumerate(media_results):
            loggers.info(f"Creating clip {i+1}/{len(media_results)}")

            # Determine clip duration
            if audio_duration and include_audio:
                clip_duration = audio_duration
            else:
                clip_duration = video_duration_per_image

            # Create image clip
            img_clip = ImageClip(image_path, duration=clip_duration)

            # Add audio if available
            if audio_path:
                try:
                    audio_clip = AudioFileClip(audio_path)
                    img_clip = img_clip.set_audio(audio_clip)
                except Exception as e:
                    loggers.warning(f"Failed to add audio to clip {i+1}: {str(e)}")

            video_clips.append(img_clip)
            total_duration += clip_duration

        clip_creation_time = time.time() - clip_creation_start
        loggers.info(f"Created {len(video_clips)} video clips in {clip_creation_time:.2f}s")

        if not video_clips:
            raise HTTPException(status_code=400, detail="No valid video clips could be created")

        # Add outro image with fade to black
        try:
            # Path to company picture (relative to project root)
            # Get the project root directory (assuming this file is in app/v1/api/management_service/routes/sharepost/)
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.join(current_dir, "../../../../../..")
            company_picture_path = os.path.join(project_root, "companypicture.png")
            company_picture_path = os.path.abspath(company_picture_path)

            if os.path.exists(company_picture_path):
                loggers.info("Adding outro image with fade to black")

                # Create outro image clip
                outro_clip = ImageClip(company_picture_path, duration=outro_duration)

                # Apply fade out effect to the outro clip (fade to black in the last 1 second)
                outro_clip = outro_clip.fadeout(1.0)  # 1 second fade to black

                video_clips.append(outro_clip)
                total_duration += outro_duration

                loggers.info(f"Added outro image with {outro_duration}s duration including fade to black")
            else:
                loggers.warning(f"Company picture not found at {company_picture_path}, skipping outro")

        except Exception as e:
            loggers.warning(f"Failed to add outro image: {str(e)}")

        # Concatenate all clips
        loggers.info(f"Concatenating {len(video_clips)} clips with total duration {total_duration:.2f}s")
        final_video = concatenate_videoclips(video_clips, method="compose")

        # Export video with timeout and fallback
        output_path = os.path.join(temp_dir, "slideshow_video.mp4")
        export_start = time.time()

        loggers.info(f"Starting video export with {export_timeout}s timeout")
        final_output_path = await export_video_with_timeout(
            final_video,
            output_path,
            temp_dir,
            export_timeout,
            include_audio
        )

        export_time = time.time() - export_start
        total_time = time.time() - start_time
        loggers.info(f"Video export completed in {export_time:.2f}s. Total processing time: {total_time:.2f}s")

        # Clean up clips
        for clip in video_clips:
            clip.close()
        final_video.close()

        loggers.info(f"Video created successfully: {final_output_path}")
        return final_output_path

    except Exception as e:
        loggers.error(f"Error creating slideshow video: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating video: {str(e)}")


@router.post("/generate_video", response_model=GenerateVideoResponse)
async def generate_video(
    request: GenerateVideoRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Generate a slideshow video from task set stories with images and audio.

    This endpoint:
    1. Fetches the task set and associated stories from the database
    2. Downloads images and audio files from MinIO URLs
    3. Creates a slideshow video using MoviePy
    4. Adds a company outro image with fade to black at the end
    5. Uploads the generated video back to MinIO
    6. Returns the video URL and metadata

    Args:
        request: Video generation request with task_set_id and options
        current_user: Current authenticated user with database access

    Returns:
        GenerateVideoResponse with video URL and metadata
    """
    try:
        # Check if MoviePy is available
        if not MOVIEPY_AVAILABLE:
            raise HTTPException(
                status_code=500,
                detail="Video generation is not available. MoviePy library is not properly installed."
            )

        loggers.info(f"Starting video generation for task set: {request.task_set_id}")

        # Fetch stories from task set
        stories = await fetch_task_set_stories(current_user, request.task_set_id)

        # Validate that stories have required media
        valid_stories = []
        for story in stories:
            image_url = story.get("image_metadata", {}).get("url")
            if image_url:
                valid_stories.append(story)
            else:
                loggers.warning(f"Story stage {story.get('stage')} has no image URL, skipping")

        if not valid_stories:
            raise HTTPException(
                status_code=400,
                detail="No stories with valid images found in task set"
            )

        loggers.info(f"Found {len(valid_stories)} valid stories with images")

        # Create temporary directory for processing
        with tempfile.TemporaryDirectory() as temp_dir:
            loggers.info(f"Created temporary directory: {temp_dir}")

            # Create slideshow video with optimized parallel processing
            video_path = await create_slideshow_video(
                stories=valid_stories,
                temp_dir=temp_dir,
                include_audio=request.include_audio,
                video_duration_per_image=request.video_duration_per_image,
                outro_duration=1.5,  # 1.5 seconds for outro image with fade
                max_concurrent_downloads=request.max_concurrent_downloads,
                export_timeout=request.export_timeout
            )

            # Read video file
            with open(video_path, 'rb') as video_file:
                video_data = video_file.read()

            loggers.info(f"Video file size: {len(video_data)} bytes")

            # Upload to MinIO
            async_minio_client = create_async_minio_client(current_user.minio)

            video_filename = f"slideshow_{request.task_set_id}_{uuid.uuid4().hex[:8]}.mp4"

            file_info = await async_minio_client.save_file_async(
                data=video_data,
                user_id=current_user.user.id,
                content_type="video/mp4",
                folder="videogen",
                custom_filename=video_filename
            )

            loggers.info(f"Video uploaded to MinIO: {file_info.get('object_name')}")

            # Prepare metadata
            video_metadata = {
                **file_info,
                "task_set_id": request.task_set_id,
                "user_id": current_user.user.id,
                "stories_count": len(valid_stories),
                "include_audio": request.include_audio,
                "video_duration_per_image": request.video_duration_per_image,
                "generation_timestamp": datetime.datetime.now(timezone.utc).isoformat(),
                "stories_processed": [
                    {
                        "stage": story.get("stage"),
                        "script_preview": story.get("script", "")[:100] + "..." if len(story.get("script", "")) > 100 else story.get("script", ""),
                        "has_audio": bool(story.get("audio_metadata", {}).get("url")),
                        "has_image": bool(story.get("image_metadata", {}).get("url"))
                    }
                    for story in valid_stories
                ]
            }

            # Optionally save video metadata to database
            try:
                await current_user.async_db.generated_videos.insert_one({
                    "_id": ObjectId(),
                    "task_set_id": ObjectId(request.task_set_id),
                    "user_id": ObjectId(current_user.user.id),
                    "video_metadata": video_metadata,
                    "created_at": datetime.datetime.now(timezone.utc),
                    "status": "completed"
                })
                loggers.info("Video metadata saved to database")
            except Exception as e:
                loggers.warning(f"Failed to save video metadata to database: {str(e)}")

            return GenerateVideoResponse(
                video_url=file_info.get("url", ""),
                video_metadata=video_metadata,
                processing_status="completed"
            )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        loggers.error(f"Unexpected error in video generation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Video generation failed: {str(e)}"
        )


@router.get("/video_status/{task_set_id}", response_model=List[Dict[str, Any]])
async def get_video_status(
    task_set_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get the status of generated videos for a task set.

    Args:
        task_set_id: The task set ID to check for generated videos
        current_user: Current authenticated user

    Returns:
        List of video generation records for the task set
    """
    try:
        # Find all generated videos for this task set
        videos = await current_user.async_db.generated_videos.find(
            {"task_set_id": ObjectId(task_set_id)},
            {"video_metadata": 1, "created_at": 1, "status": 1}
        ).sort("created_at", -1).to_list(length=None)

        # Convert ObjectIds to strings for JSON serialization
        result = []
        for video in videos:
            video_data = {
                "video_id": str(video["_id"]),
                "status": video.get("status", "unknown"),
                "created_at": video.get("created_at"),
                "video_url": video.get("video_metadata", {}).get("url"),
                "stories_count": video.get("video_metadata", {}).get("stories_count", 0),
                "include_audio": video.get("video_metadata", {}).get("include_audio", False),
                "file_size_bytes": video.get("video_metadata", {}).get("size_bytes", 0)
            }
            result.append(video_data)

        return result

    except Exception as e:
        loggers.error(f"Error fetching video status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching video status: {str(e)}"
        )


@router.get("/task_set/{task_set_id}/preview")
async def preview_task_set_media(
    task_set_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Preview the media content available in a task set for video generation.

    Args:
        task_set_id: The task set ID to preview
        current_user: Current authenticated user

    Returns:
        Preview information about available images and audio
    """
    try:
        # Fetch stories from task set
        stories = await fetch_task_set_stories(current_user, task_set_id)

        preview_data = {
            "task_set_id": task_set_id,
            "total_stories": len(stories),
            "stories_with_images": 0,
            "stories_with_audio": 0,
            "stories_preview": []
        }

        for story in stories:
            image_url = story.get("image_metadata", {}).get("url")
            audio_url = story.get("audio_metadata", {}).get("url")

            if image_url:
                preview_data["stories_with_images"] += 1
            if audio_url:
                preview_data["stories_with_audio"] += 1

            story_preview = {
                "stage": story.get("stage"),
                "script_preview": story.get("script", "")[:100] + "..." if len(story.get("script", "")) > 100 else story.get("script", ""),
                "has_image": bool(image_url),
                "has_audio": bool(audio_url),
                "image_url": image_url,
                "audio_url": audio_url
            }
            preview_data["stories_preview"].append(story_preview)

        return preview_data

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error previewing task set media: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error previewing media: {str(e)}"
        )
