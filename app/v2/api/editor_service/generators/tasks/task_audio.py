# To run this code you need to install the following dependencies:
# pip install google-genai

import mimetypes
import os
import struct
from app.v2.api.socket_service_v2.generator.edgettsgen import generate_audio_bytes as generate_edge_audio_bytes
from app.v2.api.socket_service_v2.generator.piperttsgen import generate_audio_bytes as generate_piper_audio_bytes
from typing import Union
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
import hashlib
loggers = setup_new_logging(__name__)


def convert_to_wav(audio_data: bytes, mime_type: str) -> bytes:
    """Generates a WAV file header for the given audio data and parameters.

    Args:
        audio_data: The raw audio data as a bytes object.
        mime_type: Mime type of the audio data.

    Returns:
        A bytes object representing the WAV file header.
    """
    parameters = parse_audio_mime_type(mime_type)
    bits_per_sample = parameters["bits_per_sample"]
    sample_rate = parameters["rate"]
    num_channels = 1
    data_size = len(audio_data)
    bytes_per_sample = bits_per_sample // 8
    block_align = num_channels * bytes_per_sample
    byte_rate = sample_rate * block_align
    chunk_size = 36 + data_size  # 36 bytes for header fields before data chunk size

    # http://soundfile.sapp.org/doc/WaveFormat/

    header = struct.pack(
        "<4sI4s4sIHHIIHH4sI",
        b"RIFF",          # ChunkID
        chunk_size,       # ChunkSize (total file size - 8 bytes)
        b"WAVE",          # Format
        b"fmt ",          # Subchunk1ID
        16,               # Subchunk1Size (16 for PCM)
        1,                # AudioFormat (1 for PCM)
        num_channels,     # NumChannels
        sample_rate,      # SampleRate
        byte_rate,        # ByteRate
        block_align,      # BlockAlign
        bits_per_sample,  # BitsPerSample
        b"data",          # Subchunk2ID
        data_size         # Subchunk2Size (size of audio data)
    )
    return header + audio_data


def parse_audio_mime_type(mime_type: str) -> dict[str, int]:
    """Parses bits per sample and rate from an audio MIME type string.

    Assumes bits per sample is encoded like "L16" and rate as "rate=xxxxx".

    Args:
        mime_type: The audio MIME type string (e.g., "audio/L16;rate=24000").

    Returns:
        A dictionary with "bits_per_sample" and "rate" keys. Values will be
        integers if found, otherwise None.
    """
    bits_per_sample = 16
    rate = 24000

    # Extract rate from parameters
    parts = mime_type.split(";")
    for param in parts: # Skip the main type part
        param = param.strip()
        if param.lower().startswith("rate="):
            try:
                rate_str = param.split("=", 1)[1]
                rate = int(rate_str)
            except (ValueError, IndexError):
                # Handle cases like "rate=" with no value or non-integer value
                pass # Keep rate as default
        elif param.startswith("audio/L"):
            try:
                bits_per_sample = int(param.split("L", 1)[1])
            except (ValueError, IndexError):
                pass # Keep bits_per_sample as default if conversion fails

    return {"bits_per_sample": bits_per_sample, "rate": rate}


async def generate(current_user: UserTenantDB, keyword: Union[str, list[str]] ,type:str="audio_prompt", use_piper_tts: bool = True):
    if isinstance(keyword,list):
        keyword = ", ".join(keyword)
    loggers.info(f"Generating audio for keyword: {keyword}")

    file_text = ""
    usage_metadata = {}

    try:
        if use_piper_tts:
            # Use PiperTTS to generate audio (primary choice)
            loggers.debug(f"Generating audio using PiperTTS for: {keyword}")
            file_bytes = await generate_piper_audio_bytes(
                text=keyword,
                gender="finetune_girl_2",
                speaker=0,
                length_scale=1.4,
                noise_scale=0.667,
                noise_w=0.8,
                sentence_silence=0
            )

            if not file_bytes:
                loggers.warning(f"PiperTTS failed for '{keyword}', trying EdgeTTS fallback")
                # Fallback to EdgeTTS if PiperTTS fails
                file_bytes = await generate_edge_audio_bytes(
                    text=keyword,
                    gender="Female",
                    language="ne",
                    rate="-10%"
                )
        else:
            # Use EdgeTTS directly
            loggers.debug(f"Generating audio using EdgeTTS for: {keyword}")
            file_bytes = await generate_edge_audio_bytes(
                text=keyword,
                gender="Female",
                language="ne",
                rate="-10%"
            )

    except Exception as api_error:
        loggers.error(f"Audio generation failed for '{keyword}': {api_error}")
        return None, None, {}

    # Check if we got audio data
    if not file_bytes:
        loggers.error(f"No audio data received for: {keyword}")
        return None, None, usage_metadata

    # Determine file extension and content type
    if file_bytes.startswith(b'RIFF'):
        file_extension = ".wav"
        content_type = "audio/wav"
    else:
        file_extension = ".mp3"
        content_type = "audio/mpeg"

    # Save to MinIO
    from app.shared.async_minio_client import create_async_minio_client

    async_minio_client = create_async_minio_client(current_user.minio)
    file_info = await async_minio_client.save_file_async(
        data=file_bytes,
        user_id=current_user.user.id,
        content_type=content_type,
        folder="audiogen",
        file_extension=file_extension,
        # custom_filename=hashlib.sha256(f"{keyword}_audio{file_extension}").hexdigest()[:10] 
        custom_filename=hashlib.sha256(f"{keyword}_audio{file_extension}".encode()).hexdigest()[:10] + file_extension
    )

    loggers.info(f"Audio generated and saved: {keyword}")
    return file_text, file_info, usage_metadata





if __name__ == "__main__":
    # For testing without user context, create a simple test
    print("Audio generation function updated to work with user context and Minio")
    print("Use this function in the task generation system with proper UserTenantDB context")