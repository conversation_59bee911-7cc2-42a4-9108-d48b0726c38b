# To run this code you need to install the following dependencies:
# pip install google-genai

import mimetypes
import os
import struct
# from google import genai
# from google.genai import types
from typing import Union
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
import hashlib
from datetime import datetime, timezone, timedelta

from app.v2.api.socket_service_v2.generator.edgettsgen import generate_audio_bytes as generate_edge_audio_bytes
from app.v2.api.socket_service_v2.generator.piperttsgen import generate_audio_bytes as generate_piper_audio_bytes

loggers = setup_new_logging(__name__)


def convert_to_wav(audio_data: bytes, mime_type: str) -> bytes:
    """Generates a WAV file header for the given audio data and parameters.

    Args:
        audio_data: The raw audio data as a bytes object.
        mime_type: Mime type of the audio data.

    Returns:
        A bytes object representing the WAV file header.
    """
    parameters = parse_audio_mime_type(mime_type)
    bits_per_sample = parameters["bits_per_sample"]
    sample_rate = parameters["rate"]
    num_channels = 1
    data_size = len(audio_data)
    bytes_per_sample = bits_per_sample // 8
    block_align = num_channels * bytes_per_sample
    byte_rate = sample_rate * block_align
    chunk_size = 36 + data_size  # 36 bytes for header fields before data chunk size

    # http://soundfile.sapp.org/doc/WaveFormat/

    header = struct.pack(
        "<4sI4s4sIHHIIHH4sI",
        b"RIFF",          # ChunkID
        chunk_size,       # ChunkSize (total file size - 8 bytes)
        b"WAVE",          # Format
        b"fmt ",          # Subchunk1ID
        16,               # Subchunk1Size (16 for PCM)
        1,                # AudioFormat (1 for PCM)
        num_channels,     # NumChannels
        sample_rate,      # SampleRate
        byte_rate,        # ByteRate
        block_align,      # BlockAlign
        bits_per_sample,  # BitsPerSample
        b"data",          # Subchunk2ID
        data_size         # Subchunk2Size (size of audio data)
    )
    return header + audio_data


def parse_audio_mime_type(mime_type: str) -> dict[str, int]:
    """Parses bits per sample and rate from an audio MIME type string.

    Assumes bits per sample is encoded like "L16" and rate as "rate=xxxxx".

    Args:
        mime_type: The audio MIME type string (e.g., "audio/L16;rate=24000").

    Returns:
        A dictionary with "bits_per_sample" and "rate" keys. Values will be
        integers if found, otherwise None.
    """
    bits_per_sample = 16
    rate = 24000

    # Extract rate from parameters
    parts = mime_type.split(";")
    for param in parts: # Skip the main type part
        param = param.strip()
        if param.lower().startswith("rate="):
            try:
                rate_str = param.split("=", 1)[1]
                rate = int(rate_str)
            except (ValueError, IndexError):
                # Handle cases like "rate=" with no value or non-integer value
                pass # Keep rate as default
        elif param.startswith("audio/L"):
            try:
                bits_per_sample = int(param.split("L", 1)[1])
            except (ValueError, IndexError):
                pass # Keep bits_per_sample as default if conversion fails

    return {"bits_per_sample": bits_per_sample, "rate": rate}


async def generate_audio(current_user: UserTenantDB, keyword: Union[str, list[str]] ,type:str="audio_prompt"):
    if isinstance(keyword,list):
        keyword = ", ".join(keyword)
    loggers.info(f"Generating audio for keyword: {keyword}")

    # Check if audio already exists in media cache
    cached_media = await _check_media_cache(current_user, keyword, "audio", type)
    if cached_media:
        loggers.info(f"🎯 Using cached audio for keyword: {keyword}")
        return cached_media["file_text"], cached_media["file_info"], cached_media["usage_metadata"]

    
    file_text = ""

    # Try PiperTTS first, fallback to EdgeTTS if needed
    try:
        loggers.debug(f"Generating audio using PiperTTS for: {keyword}")
        file_bytes = await generate_piper_audio_bytes(
            text=keyword,
            gender="finetune_girl_2",
            speaker=0,
            length_scale=1.4,
            noise_scale=0.667,
            noise_w=0.8,
            sentence_silence=0
        )

        if not file_bytes:
            loggers.warning(f"PiperTTS failed for '{keyword}', trying EdgeTTS fallback")
            file_bytes = await generate_edge_audio_bytes(text=keyword)

    except Exception as e:
        loggers.error(f"PiperTTS failed for '{keyword}': {e}, trying EdgeTTS fallback")
        file_bytes = await generate_edge_audio_bytes(text=keyword)

    usage_metadata = {}

    # Check if we got audio data
    if not file_bytes:
        loggers.error(f"No audio data received for: {keyword}")
        return None, None, usage_metadata

    # Determine file extension and content type
    # if file_bytes.startswith(b'RIFF'):
    #     file_extension = ".wav"
    #     content_type = "audio/wav"
    # else:
    #     file_extension = ".mp3"
    #     content_type = "audio/mpeg"

    print("Audio bytes", file_bytes[:10])
    if file_bytes.startswith(b'RIFF') and file_bytes[8:12] == b'WAVE':
        print("WAV format detected")
        file_extension = ".wav"
        content_type = "audio/wav"
    elif file_bytes.startswith(b'ID3') or file_bytes[0:2] == b'\xFF\xFB':
        # return ".mp3", "audio/mpeg"
        print("MP3 format detected")
        file_extension = ".mp3"
        content_type = "audio/mpeg"
    elif file_bytes[:2] in [b'\xFF\xFB', b'\xFF\xF3', b'\xFF\xF2']:  # Raw MP3 frames
        # return ".mp3", "audio/mpeg"
        print("MP3 RAW format detected")
        file_extension = ".mp3"
        content_type = "audio/mpeg"
    elif file_bytes.startswith(b'OggS'):
        # return ".ogg", "audio/ogg"
        print("Ogg format detected")
        file_extension = ".ogg"
        content_type = "audio/ogg"
    elif file_bytes.startswith(b'\x1A\x45\xDF\xA3'):  # EBML header (used in WebM and MKV)
        # return ".webm", "audio/webm"
        print("WebM format detected")
        file_extension = ".webm"
        content_type = "audio/webm"
    else:
        # return None, None  # or raise ValueError("Unsupported audio format")
        raise ValueError("Unsupported audio format")

    # Save to MinIO
    from app.shared.async_minio_client import create_async_minio_client

    async_minio_client = create_async_minio_client(current_user.minio)
    file_info = await async_minio_client.save_file_async(
        data=file_bytes,
        user_id=current_user.user.id,
        content_type=content_type,
        folder="audiogen",
        file_extension=file_extension,
        # custom_filename=hashlib.sha256(f"{keyword}_audio{file_extension}").hexdigest()[:10] 
        custom_filename=hashlib.sha256(f"{keyword}_audio{file_extension}".encode()).hexdigest()[:10] + file_extension
    )

    loggers.info(f"Audio generated and saved: {keyword}")

    # Cache the generated audio for future use
    await _save_media_cache(current_user, keyword, "audio", type, file_text, file_info, usage_metadata)

    return file_text, file_info, usage_metadata





async def _check_media_cache(current_user: UserTenantDB, keyword: str, media_type: str, prompt_type: str):
    """
    Check if media already exists in cache for the given keyword with multi-user support.

    Args:
        current_user: User context with database access
        keyword: The keyword/text used to generate media
        media_type: Type of media ("audio" or "image")
        prompt_type: Type of prompt used (e.g., "audio_prompt", "imagen_prompt")

    Returns:
        Cached media data if found, None otherwise
    """
    try:
        # Check if media exists in cache
        cached_media = await current_user.async_db.media.find_one({
            "keyword": keyword,
            "media_type": media_type,
            "prompt_type": prompt_type
        })

        if cached_media:
            # Update user access tracking
            user_ids = cached_media.get("user_ids", [])
            if current_user.user.id not in user_ids:
                user_ids.append(current_user.user.id)
                await current_user.async_db.media.update_one(
                    {"_id": cached_media["_id"]},
                    {
                        "$set": {
                            "user_ids": user_ids,
                            "last_accessed_at": datetime.now(timezone.utc),
                            "last_accessed_by": current_user.user.id
                        }
                    }
                )
            else:
                # Just update last access time
                await current_user.async_db.media.update_one(
                    {"_id": cached_media["_id"]},
                    {
                        "$set": {
                            "last_accessed_at": datetime.now(timezone.utc),
                            "last_accessed_by": current_user.user.id
                        }
                    }
                )

            loggers.info(f"🎯 Found cached {media_type} for keyword: {keyword}")

            # Generate fresh presigned URL for the cached media
            try:
                presigned_url = current_user.minio.get_presigned_url(
                    bucket_name=current_user.minio_bucket_name,
                    object_name=cached_media["object_name"],
                    expires=timedelta(hours=24),
                    method="GET"
                )

                # Update file_info with fresh URL
                file_info = cached_media["file_info"].copy()
                file_info["url"] = presigned_url

                return {
                    "file_text": cached_media.get("file_text", ""),
                    "file_info": file_info,
                    "usage_metadata": cached_media.get("usage_metadata", {})
                }

            except Exception as url_error:
                loggers.error(f"❌ Error generating presigned URL for cached media: {url_error}")
                # If URL generation fails, we'll regenerate the media
                return None

        return None

    except Exception as e:
        loggers.error(f"❌ Error checking media cache: {e}")
        return None


async def _save_media_cache(current_user: UserTenantDB, keyword: str, media_type: str, prompt_type: str,
                           file_text: str, file_info: dict, usage_metadata: dict):
    """
    Save generated media to cache for future reuse with multi-user support.

    Args:
        current_user: User context with database access
        keyword: The keyword/text used to generate media
        media_type: Type of media ("audio" or "image")
        prompt_type: Type of prompt used (e.g., "audio_prompt", "imagen_prompt")
        file_text: Generated text content
        file_info: File information from MinIO
        usage_metadata: API usage metadata
    """
    try:
        # Create a unique cache key
        cache_key = hashlib.sha256(f"{keyword}_{media_type}_{prompt_type}".encode()).hexdigest()

        # Check if media already exists
        existing_media = await current_user.async_db.media.find_one({
            "cache_key": cache_key
        })

        if existing_media:
            # Add current user to user_ids list if not already present
            user_ids = existing_media.get("user_ids", [])
            if current_user.user.id not in user_ids:
                user_ids.append(current_user.user.id)

            await current_user.async_db.media.update_one(
                {"cache_key": cache_key},
                {
                    "$set": {
                        "user_ids": user_ids,
                        "last_accessed_at": datetime.now(timezone.utc),
                        "last_accessed_by": current_user.user.id
                    }
                }
            )
            loggers.info(f"💾 Updated user access for cached {media_type} for keyword: {keyword}")
        else:
            # Prepare cache document with user_ids list
            cache_document = {
                "cache_key": cache_key,
                "keyword": keyword,
                "media_type": media_type,
                "prompt_type": prompt_type,
                "file_text": file_text,
                "file_info": file_info,
                "usage_metadata": usage_metadata.model_dump() if hasattr(usage_metadata, 'model_dump') else usage_metadata,
                "object_name": file_info.get("object_name"),
                "folder": file_info.get("folder"),
                "content_type": file_info.get("content_type"),
                "file_size": file_info.get("file_size"),
                "created_at": datetime.now(timezone.utc),
                "user_ids": [current_user.user.id],  # Store as list for multi-user access
                "created_by": current_user.user.id,
                "last_accessed_at": datetime.now(timezone.utc),
                "last_accessed_by": current_user.user.id
            }

            # Save to media collection (upsert to avoid duplicates)
            await current_user.async_db.media.update_one(
                {"cache_key": cache_key},
                {"$set": cache_document},
                upsert=True
            )
            loggers.info(f"💾 Cached {media_type} for keyword: {keyword} with multi-user support")

    except Exception as e:
        loggers.error(f"❌ Error saving media cache: {e}")
        # Don't fail the main operation if caching fails


if __name__ == "__main__":
    # For testing without user context, create a simple test
    print("Audio generation function updated to work with user context and Minio")
    print("Use this function in the task generation system with proper UserTenantDB context")