import random
from typing import Optional, Literal
import httpx
import urllib.parse
from fastapi.responses import StreamingResponse
import io

from app.shared.utils.logger import setup_new_logging

loggers = setup_new_logging(__name__)

async def generate_audio_bytes(
    text: str,
    gender: Optional[str] = "finetune_girl_2",
    speaker: Optional[int] = 0,
    length_scale: Optional[float] = 1.4,
    noise_scale: Optional[float] = 0.667,
    noise_w: Optional[float] = 0.8,
    sentence_silence: Optional[float] = 0,
    piper_tts_url: str = "http://*************:5000/tts"
) -> bytes | None:
    """
    Generates audio bytes from text using PiperTTS.
    Supports Nepali text and various voice parameters.

    Args:
        text (str): The text to convert to speech (supports Nepali).
        gender (Optional[str]): Voice gender/model (e.g., "finetune_girl_2").
        speaker (Optional[int]): Speaker ID.
        length_scale (Optional[float]): Speech rate control (higher = slower).
        noise_scale (Optional[float]): Noise level control.
        noise_w (Optional[float]): Noise width control.
        sentence_silence (Optional[float]): Silence between sentences.
        piper_tts_url (str): PiperTTS service URL.

    Returns:
        bytes: The generated audio as WAV bytes.

    Raises:
        ValueError: If text is empty or invalid parameters.
        Exception: For any other errors during audio generation.
    """
    if not text or not text.strip():
        raise ValueError("Text cannot be empty")

    try:
        # URL encode the text to handle Nepali characters properly
        encoded_text = urllib.parse.quote(text)

        # Prepare form data for the PiperTTS API
        form_data = {
            'text': encoded_text,
            'gender': gender or "finetune_girl_2",
            'speaker': str(speaker or 0),
            'length_scale': str(length_scale or 1.4),
            'noise_scale': str(noise_scale or 0.667),
            'noise_w': str(noise_w or 0.8),
            'sentence_silence': str(sentence_silence or 0)
        }

        loggers.info(f"Generating PiperTTS audio for text: '{text[:50]}...' with gender: {gender}")

        # Make request to PiperTTS service
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                piper_tts_url,
                headers={
                    'accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data=form_data
            )

            response.raise_for_status()

            # Get the audio content
            audio_content = response.content

            if not audio_content:
                loggers.error("Received empty audio content from PiperTTS service")
                return None

            loggers.info(f"Successfully generated PiperTTS audio, size: {len(audio_content)} bytes")
            return audio_content

    except httpx.TimeoutException:
        loggers.error("PiperTTS request timed out")
        return None
    except httpx.HTTPStatusError as e:
        loggers.error(f"PiperTTS HTTP error: {e.response.status_code} - {e.response.text}")
        return None
    except Exception as e:
        loggers.error(f"Failed to generate PiperTTS audio: {e}")
        return None


async def generate_streaming_audio(
    text: str,
    gender: Optional[str] = "finetune_girl_2",
    speaker: Optional[int] = 0,
    length_scale: Optional[float] = 1.4,
    noise_scale: Optional[float] = 0.667,
    noise_w: Optional[float] = 0.8,
    sentence_silence: Optional[float] = 0,
    piper_tts_url: str = "http://*************:5000/tts"
) -> StreamingResponse | None:
    """
    Generates audio using PiperTTS service and returns a StreamingResponse.

    Args:
        text (str): The text to convert to speech (supports Nepali text)
        gender (Optional[str]): Voice gender/model (e.g., "finetune_girl_2")
        speaker (Optional[int]): Speaker ID
        length_scale (Optional[float]): Speech rate control (higher = slower)
        noise_scale (Optional[float]): Noise level control
        noise_w (Optional[float]): Noise width control
        sentence_silence (Optional[float]): Silence between sentences
        piper_tts_url (str): PiperTTS service URL

    Returns:
        StreamingResponse: Audio file as streaming response
        None: If generation fails
    """
    try:
        # Generate audio bytes
        audio_bytes = await generate_audio_bytes(
            text=text,
            gender=gender,
            speaker=speaker,
            length_scale=length_scale,
            noise_scale=noise_scale,
            noise_w=noise_w,
            sentence_silence=sentence_silence,
            piper_tts_url=piper_tts_url
        )

        if not audio_bytes:
            return None

        # Create a streaming response
        return StreamingResponse(
            io.BytesIO(audio_bytes),
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=piper_audio.wav",
                "Content-Length": str(len(audio_bytes))
            }
        )

    except Exception as e:
        loggers.error(f"Failed to create streaming response: {e}")
        return None


if __name__ == "__main__":
    import asyncio

    async def main():
        # Test with Nepali text (same as your curl example)
        nepali_text = "तपाईंको उडान बुक गरिएको छ। तपाईंको उडान नम्बर एक दुई चार चार दुई हो।"

        print(f"\nTesting PiperTTS with Nepali text: '{nepali_text}'")

        # Test audio bytes generation
        audio_data = await generate_audio_bytes(
            text=nepali_text,
            gender="finetune_girl_2",
            speaker=0,
            length_scale=1.4,
            noise_scale=0.667,
            noise_w=0.8,
            sentence_silence=0
        )

        if audio_data:
            output_filename = "piper_output_audio.wav"
            try:
                with open(output_filename, "wb") as f:
                    f.write(audio_data)
                print(f"PiperTTS audio successfully saved to {output_filename}")
                print(f"Audio size: {len(audio_data)} bytes")
            except IOError as e:
                print(f"Error saving audio to file: {e}")
        else:
            print("Failed to generate PiperTTS audio.")

        # Test with different voice parameters
        print(f"\nTesting with different voice parameters...")
        audio_data_2 = await generate_audio_bytes(
            text="नमस्ते, यो एक परीक्षण हो।",  # "Hello, this is a test." in Nepali
            gender="finetune_girl_2",
            speaker=1,
            length_scale=1.0,  # Faster speech
            noise_scale=0.5,
            noise_w=0.6
        )

        if audio_data_2:
            output_filename_2 = "piper_test_audio.wav"
            try:
                with open(output_filename_2, "wb") as f:
                    f.write(audio_data_2)
                print(f"Second PiperTTS audio saved to {output_filename_2}")
            except IOError as e:
                print(f"Error saving second audio: {e}")

    asyncio.run(main())